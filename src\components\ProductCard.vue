<template>
  <div class="product-card" :class="{ 'out-of-stock': !product.inStock }">
    <div class="card-image">
      <img :src="product.image" :alt="product.name" />
      <div v-if="!product.inStock" class="out-of-stock-overlay">
        <span>Out of Stock</span>
      </div>
      <div v-if="product.originalPrice > product.price" class="discount-badge">
        {{ Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100) }}% OFF
      </div>
    </div>
    
    <div class="card-content">
      <div class="card-header">
        <h3 class="product-name">{{ product.name }}</h3>
        <div class="brand-category">
          <span class="brand">{{ product.brand }}</span>
          <span class="category">{{ product.category }}</span>
        </div>
      </div>
      
      <p class="product-description">{{ product.description }}</p>
      
      <div class="product-features">
        <div class="feature-item" v-for="feature in product.features.slice(0, 2)" :key="feature">
          <i class="fas fa-check"></i>
          <span>{{ feature }}</span>
        </div>
      </div>
      
      <div class="compatibility">
        <span class="compatibility-label">Compatible with:</span>
        <div class="vehicle-types">
          <span 
            v-for="type in product.compatibility" 
            :key="type" 
            class="vehicle-type"
          >
            {{ type }}
          </span>
        </div>
      </div>
      
      <div class="rating-reviews">
        <div class="rating">
          <div class="stars">
            <i 
              v-for="star in 5" 
              :key="star"
              class="fas fa-star"
              :class="{ filled: star <= Math.floor(product.rating) }"
            ></i>
          </div>
          <span class="rating-value">{{ product.rating }}</span>
        </div>
        <span class="reviews-count">({{ product.reviews }} reviews)</span>
      </div>
      
      <div class="card-footer">
        <div class="pricing">
          <div v-if="product.originalPrice > product.price" class="original-price">
            ${{ product.originalPrice.toFixed(2) }}
          </div>
          <div class="current-price">${{ product.price.toFixed(2) }}</div>
        </div>
        
        <div class="actions">
          <div class="quantity-selector">
            <button 
              @click="decrementQuantity" 
              :disabled="quantity <= 1"
              class="qty-btn"
            >
              <i class="fas fa-minus"></i>
            </button>
            <input 
              v-model.number="quantity" 
              type="number" 
              min="1" 
              max="99"
              class="qty-input"
            >
            <button 
              @click="incrementQuantity" 
              :disabled="quantity >= 99"
              class="qty-btn"
            >
              <i class="fas fa-plus"></i>
            </button>
          </div>
          
          <button 
            @click="addToQuote" 
            :disabled="!product.inStock"
            class="add-to-quote-btn"
          >
            <i class="fas fa-plus"></i>
            Add to Quote
          </button>
        </div>
      </div>
      
      <div class="product-meta">
        <div class="part-number">Part #: {{ product.partNumber }}</div>
        <div class="warranty">Warranty: {{ product.warranty }}</div>
        <div class="stock-info" :class="{ 'low-stock': product.stockQuantity < 10 }">
          {{ product.inStock ? `${product.stockQuantity} in stock` : 'Out of stock' }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  product: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['add-to-quote'])

const quantity = ref(1)

function incrementQuantity() {
  if (quantity.value < 99) {
    quantity.value++
  }
}

function decrementQuantity() {
  if (quantity.value > 1) {
    quantity.value--
  }
}

function addToQuote() {
  if (props.product.inStock) {
    emit('add-to-quote', props.product, quantity.value)
    quantity.value = 1 // Reset quantity after adding
  }
}
</script>

<style lang="scss" scoped>
.product-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  &.out-of-stock {
    opacity: 0.7;
  }
}

.card-image {
  position: relative;
  height: 200px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  &:hover img {
    transform: scale(1.05);
  }

  .out-of-stock-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
  }

  .discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #e74c3c;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
  }
}

.card-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-header {
  margin-bottom: 12px;

  .product-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #2c3e50;
  }

  .brand-category {
    display: flex;
    gap: 12px;
    
    .brand {
      color: #3498db;
      font-weight: 500;
    }
    
    .category {
      color: #7f8c8d;
      font-size: 0.9rem;
    }
  }
}

.product-description {
  color: #5a6c7d;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 12px;
}

.product-features {
  margin-bottom: 12px;

  .feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    font-size: 0.85rem;
    color: #5a6c7d;

    .fas {
      color: #27ae60;
      font-size: 0.7rem;
    }
  }
}

.compatibility {
  margin-bottom: 12px;

  .compatibility-label {
    font-size: 0.8rem;
    color: #7f8c8d;
    display: block;
    margin-bottom: 4px;
  }

  .vehicle-types {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .vehicle-type {
      background: #ecf0f1;
      color: #2c3e50;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 0.75rem;
    }
  }
}

.rating-reviews {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;

  .rating {
    display: flex;
    align-items: center;
    gap: 4px;

    .stars {
      display: flex;
      gap: 2px;

      .fa-star {
        color: #bdc3c7;
        font-size: 0.8rem;

        &.filled {
          color: #f39c12;
        }
      }
    }

    .rating-value {
      font-size: 0.85rem;
      font-weight: 500;
      color: #2c3e50;
    }
  }

  .reviews-count {
    font-size: 0.8rem;
    color: #7f8c8d;
  }
}

.card-footer {
  margin-top: auto;
}

.pricing {
  margin-bottom: 16px;

  .original-price {
    text-decoration: line-through;
    color: #95a5a6;
    font-size: 0.9rem;
  }

  .current-price {
    font-size: 1.4rem;
    font-weight: 700;
    color: #27ae60;
  }
}

.actions {
  display: flex;
  gap: 12px;
  align-items: center;

  .quantity-selector {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;

    .qty-btn {
      background: #f8f9fa;
      border: none;
      padding: 8px 10px;
      cursor: pointer;
      transition: background 0.2s;

      &:hover:not(:disabled) {
        background: #e9ecef;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .qty-input {
      border: none;
      width: 50px;
      text-align: center;
      padding: 8px 4px;
      font-weight: 500;

      &:focus {
        outline: none;
      }
    }
  }

  .add-to-quote-btn {
    flex: 1;
    background: #3498db;
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    &:hover:not(:disabled) {
      background: #2980b9;
    }

    &:disabled {
      background: #bdc3c7;
      cursor: not-allowed;
    }
  }
}

.product-meta {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #ecf0f1;
  font-size: 0.8rem;
  color: #7f8c8d;

  div {
    margin-bottom: 4px;
  }

  .stock-info.low-stock {
    color: #e74c3c;
    font-weight: 500;
  }
}
</style>
