import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useQuotationStore = defineStore('quotation', () => {
  // State
  const quoteItems = ref([])
  const customerInfo = ref({
    name: '',
    email: '',
    phone: '',
    vehicleYear: '',
    vehicleMake: '',
    vehicleModel: ''
  })
  const quoteNumber = ref(generateQuoteNumber())
  const quoteDate = ref(new Date().toISOString().split('T')[0])

  // Getters
  const totalItems = computed(() => quoteItems.value.length)
  
  const subtotal = computed(() => {
    return quoteItems.value.reduce((sum, item) => {
      return sum + (item.price * item.quantity)
    }, 0)
  })
  
  const tax = computed(() => subtotal.value * 0.08) // 8% tax
  
  const total = computed(() => subtotal.value + tax.value)
  
  const totalQuantity = computed(() => {
    return quoteItems.value.reduce((sum, item) => sum + item.quantity, 0)
  })

  // Actions
  function addToQuote(product, quantity = 1) {
    const existingItem = quoteItems.value.find(item => item.id === product.id)
    
    if (existingItem) {
      existingItem.quantity += quantity
    } else {
      quoteItems.value.push({
        ...product,
        quantity,
        addedAt: new Date().toISOString()
      })
    }
  }

  function removeFromQuote(productId) {
    const index = quoteItems.value.findIndex(item => item.id === productId)
    if (index > -1) {
      quoteItems.value.splice(index, 1)
    }
  }

  function updateQuantity(productId, quantity) {
    const item = quoteItems.value.find(item => item.id === productId)
    if (item) {
      if (quantity <= 0) {
        removeFromQuote(productId)
      } else {
        item.quantity = quantity
      }
    }
  }

  function clearQuote() {
    quoteItems.value = []
  }

  function updateCustomerInfo(info) {
    customerInfo.value = { ...customerInfo.value, ...info }
  }

  function generateQuoteNumber() {
    const timestamp = Date.now().toString().slice(-6)
    const random = Math.random().toString(36).substring(2, 5).toUpperCase()
    return `Q${timestamp}${random}`
  }

  function resetQuote() {
    quoteItems.value = []
    customerInfo.value = {
      name: '',
      email: '',
      phone: '',
      vehicleYear: '',
      vehicleMake: '',
      vehicleModel: ''
    }
    quoteNumber.value = generateQuoteNumber()
    quoteDate.value = new Date().toISOString().split('T')[0]
  }

  return {
    // State
    quoteItems,
    customerInfo,
    quoteNumber,
    quoteDate,
    
    // Getters
    totalItems,
    subtotal,
    tax,
    total,
    totalQuantity,
    
    // Actions
    addToQuote,
    removeFromQuote,
    updateQuantity,
    clearQuote,
    updateCustomerInfo,
    resetQuote
  }
})
