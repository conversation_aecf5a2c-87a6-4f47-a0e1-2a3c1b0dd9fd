<template>
  <div class="quote-summary">
    <div class="quote-header">
      <div class="quote-info">
        <h1>Quotation Summary</h1>
        <div class="quote-meta">
          <span class="quote-number">Quote #{{ quotationStore.quoteNumber }}</span>
          <span class="quote-date">Date: {{ formatDate(quotationStore.quoteDate) }}</span>
        </div>
      </div>
      
      <div class="quote-actions">
        <button @click="showCustomerForm = true" class="btn btn-secondary">
          <i class="fas fa-user"></i>
          Customer Info
        </button>
        <button @click="exportToPDF" class="btn btn-primary">
          <i class="fas fa-file-pdf"></i>
          Export PDF
        </button>
        <button @click="printQuote" class="btn btn-outline">
          <i class="fas fa-print"></i>
          Print
        </button>
      </div>
    </div>

    <!-- Customer Information -->
    <div v-if="hasCustomerInfo" class="customer-section">
      <h3>Customer Information</h3>
      <div class="customer-info">
        <div class="info-row">
          <span class="label">Name:</span>
          <span>{{ quotationStore.customerInfo.name }}</span>
        </div>
        <div class="info-row">
          <span class="label">Email:</span>
          <span>{{ quotationStore.customerInfo.email }}</span>
        </div>
        <div class="info-row">
          <span class="label">Phone:</span>
          <span>{{ quotationStore.customerInfo.phone }}</span>
        </div>
        <div class="info-row">
          <span class="label">Vehicle:</span>
          <span>{{ vehicleInfo }}</span>
        </div>
      </div>
    </div>

    <!-- Quote Items -->
    <div class="quote-items-section">
      <h3>Quote Items ({{ quotationStore.totalItems }})</h3>
      
      <div v-if="quotationStore.quoteItems.length === 0" class="empty-quote">
        <i class="fas fa-shopping-cart"></i>
        <h4>No items in quote</h4>
        <p>Add some products to get started</p>
        <router-link to="/" class="btn btn-primary">
          <i class="fas fa-plus"></i>
          Browse Products
        </router-link>
      </div>
      
      <div v-else class="quote-items">
        <div class="items-header">
          <div class="col-product">Product</div>
          <div class="col-price">Unit Price</div>
          <div class="col-quantity">Quantity</div>
          <div class="col-total">Total</div>
          <div class="col-actions">Actions</div>
        </div>
        
        <div 
          v-for="item in quotationStore.quoteItems" 
          :key="item.id" 
          class="quote-item"
        >
          <div class="col-product">
            <div class="item-info">
              <img :src="item.image" :alt="item.name" class="item-image" />
              <div class="item-details">
                <h4 class="item-name">{{ item.name }}</h4>
                <p class="item-brand">{{ item.brand }}</p>
                <p class="item-part">Part #: {{ item.partNumber }}</p>
              </div>
            </div>
          </div>
          
          <div class="col-price">
            <span class="unit-price">${{ item.price.toFixed(2) }}</span>
          </div>
          
          <div class="col-quantity">
            <div class="quantity-controls">
              <button 
                @click="decrementQuantity(item.id)" 
                :disabled="item.quantity <= 1"
                class="qty-btn"
              >
                <i class="fas fa-minus"></i>
              </button>
              <input 
                :value="item.quantity"
                @input="updateQuantity(item.id, $event.target.value)"
                type="number" 
                min="1" 
                max="99"
                class="qty-input"
              >
              <button 
                @click="incrementQuantity(item.id)" 
                :disabled="item.quantity >= 99"
                class="qty-btn"
              >
                <i class="fas fa-plus"></i>
              </button>
            </div>
          </div>
          
          <div class="col-total">
            <span class="item-total">${{ (item.price * item.quantity).toFixed(2) }}</span>
          </div>
          
          <div class="col-actions">
            <button @click="removeItem(item.id)" class="remove-btn">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Quote Totals -->
    <div v-if="quotationStore.quoteItems.length > 0" class="quote-totals">
      <div class="totals-section">
        <div class="total-row">
          <span class="total-label">Subtotal:</span>
          <span class="total-value">${{ quotationStore.subtotal.toFixed(2) }}</span>
        </div>
        <div class="total-row">
          <span class="total-label">Tax (8%):</span>
          <span class="total-value">${{ quotationStore.tax.toFixed(2) }}</span>
        </div>
        <div class="total-row grand-total">
          <span class="total-label">Total:</span>
          <span class="total-value">${{ quotationStore.total.toFixed(2) }}</span>
        </div>
      </div>
      
      <div class="quote-footer-actions">
        <button @click="clearQuote" class="btn btn-danger">
          <i class="fas fa-trash"></i>
          Clear Quote
        </button>
        <button @click="sendQuote" class="btn btn-success">
          <i class="fas fa-paper-plane"></i>
          Send Quote
        </button>
      </div>
    </div>

    <!-- Customer Form Modal -->
    <CustomerForm 
      v-if="showCustomerForm"
      @close="showCustomerForm = false"
      @save="handleCustomerSave"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useQuotationStore } from '../stores/quotation'
import CustomerForm from '../components/CustomerForm.vue'

const quotationStore = useQuotationStore()
const showCustomerForm = ref(false)

const hasCustomerInfo = computed(() => {
  const info = quotationStore.customerInfo
  return info.name || info.email || info.phone
})

const vehicleInfo = computed(() => {
  const info = quotationStore.customerInfo
  const parts = [info.vehicleYear, info.vehicleMake, info.vehicleModel].filter(Boolean)
  return parts.length > 0 ? parts.join(' ') : 'Not specified'
})

function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

function updateQuantity(itemId, value) {
  const quantity = parseInt(value) || 1
  quotationStore.updateQuantity(itemId, quantity)
}

function incrementQuantity(itemId) {
  const item = quotationStore.quoteItems.find(item => item.id === itemId)
  if (item && item.quantity < 99) {
    quotationStore.updateQuantity(itemId, item.quantity + 1)
  }
}

function decrementQuantity(itemId) {
  const item = quotationStore.quoteItems.find(item => item.id === itemId)
  if (item && item.quantity > 1) {
    quotationStore.updateQuantity(itemId, item.quantity - 1)
  }
}

function removeItem(itemId) {
  quotationStore.removeFromQuote(itemId)
}

function clearQuote() {
  if (confirm('Are you sure you want to clear the entire quote?')) {
    quotationStore.clearQuote()
  }
}

function handleCustomerSave(customerData) {
  quotationStore.updateCustomerInfo(customerData)
  showCustomerForm.value = false
}

function exportToPDF() {
  // PDF export functionality will be implemented
  console.log('Exporting to PDF...')
}

function printQuote() {
  window.print()
}

function sendQuote() {
  // Email sending functionality
  console.log('Sending quote...')
}
</script>
