<template>
  <div class="product-catalog">
    <!-- Header with filters and view controls -->
    <div class="catalog-header">
      <div class="filters-section">
        <div class="search-box">
          <i class="fas fa-search"></i>
          <input 
            v-model="searchQuery" 
            type="text" 
            placeholder="Search products..."
            class="search-input"
          >
        </div>
        
        <select v-model="selectedCategory" class="filter-select">
          <option value="">All Categories</option>
          <option v-for="category in productCategories" :key="category" :value="category">
            {{ category }}
          </option>
        </select>
        
        <select v-model="selectedVehicleType" class="filter-select">
          <option value="">All Vehicle Types</option>
          <option v-for="type in vehicleTypes" :key="type" :value="type">
            {{ type }}
          </option>
        </select>
      </div>
      
      <div class="view-controls">
        <div class="view-toggle">
          <button 
            @click="currentView = 'card'" 
            :class="{ active: currentView === 'card' }"
            class="view-btn"
            title="Card View"
          >
            <i class="fas fa-th-large"></i>
          </button>
          <button 
            @click="currentView = 'list'" 
            :class="{ active: currentView === 'list' }"
            class="view-btn"
            title="List View"
          >
            <i class="fas fa-list"></i>
          </button>
          <button 
            @click="currentView = 'grid'" 
            :class="{ active: currentView === 'grid' }"
            class="view-btn"
            title="Grid View"
          >
            <i class="fas fa-th"></i>
          </button>
        </div>
        
        <div class="quote-summary">
          <span class="quote-count">{{ quotationStore.totalItems }} items in quote</span>
          <router-link to="/quote" class="quote-btn">
            <i class="fas fa-file-invoice-dollar"></i>
            View Quote (${{ quotationStore.total.toFixed(2) }})
          </router-link>
        </div>
      </div>
    </div>

    <!-- Products Display -->
    <div class="products-container">
      <div v-if="filteredProducts.length === 0" class="no-products">
        <i class="fas fa-search"></i>
        <h3>No products found</h3>
        <p>Try adjusting your search criteria</p>
      </div>
      
      <!-- Card View -->
      <div v-else-if="currentView === 'card'" class="products-card-view">
        <ProductCard 
          v-for="product in filteredProducts" 
          :key="product.id" 
          :product="product"
          @add-to-quote="handleAddToQuote"
        />
      </div>
      
      <!-- List View -->
      <div v-else-if="currentView === 'list'" class="products-list-view">
        <ProductList 
          :products="filteredProducts"
          @add-to-quote="handleAddToQuote"
        />
      </div>
      
      <!-- Grid View -->
      <div v-else-if="currentView === 'grid'" class="products-grid-view">
        <ProductGrid 
          v-for="product in filteredProducts" 
          :key="product.id" 
          :product="product"
          @add-to-quote="handleAddToQuote"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useQuotationStore } from '../stores/quotation'
import products, { productCategories, vehicleTypes } from '../data/products'
import ProductCard from '../components/ProductCard.vue'
import ProductList from '../components/ProductList.vue'
import ProductGrid from '../components/ProductGrid.vue'

// Store
const quotationStore = useQuotationStore()

// Reactive data
const currentView = ref('card')
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedVehicleType = ref('')

// Computed
const filteredProducts = computed(() => {
  let filtered = products

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(product => 
      product.name.toLowerCase().includes(query) ||
      product.brand.toLowerCase().includes(query) ||
      product.description.toLowerCase().includes(query) ||
      product.category.toLowerCase().includes(query)
    )
  }

  // Category filter
  if (selectedCategory.value) {
    filtered = filtered.filter(product => product.category === selectedCategory.value)
  }

  // Vehicle type filter
  if (selectedVehicleType.value) {
    filtered = filtered.filter(product => 
      product.compatibility.includes(selectedVehicleType.value)
    )
  }

  return filtered
})

// Methods
function handleAddToQuote(product, quantity = 1) {
  quotationStore.addToQuote(product, quantity)
  
  // Show success notification (you can implement a toast system)
  console.log(`Added ${quantity}x ${product.name} to quote`)
}

onMounted(() => {
  // Any initialization logic
})
</script>
