// Reset and base styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #2c3e50;
  background-color: #f8f9fa;
}

// Container
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

// App Layout
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .app-title {
    font-size: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;

    i {
      font-size: 1.8rem;
    }
  }

  .app-nav {
    display: flex;
    gap: 24px;

    .nav-link {
      color: white;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      border-radius: 6px;
      transition: background 0.2s;
      font-weight: 500;

      &:hover,
      &.router-link-active {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }
}

.app-main {
  flex: 1;
  padding: 2rem 0;
}

.app-footer {
  background: #2c3e50;
  color: white;
  padding: 1rem 0;
  text-align: center;
  margin-top: auto;
}

// Product Catalog Styles
.product-catalog {
  .catalog-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
    }
  }

  .filters-section {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;

    @media (max-width: 768px) {
      justify-content: center;
    }

    .search-box {
      position: relative;
      min-width: 250px;

      i {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
      }

      .search-input {
        width: 100%;
        padding: 10px 12px 10px 40px;
        border: 1px solid #ced4da;
        border-radius: 6px;
        font-size: 0.9rem;

        &:focus {
          outline: none;
          border-color: #007bff;
          box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }
      }
    }

    .filter-select {
      padding: 10px 12px;
      border: 1px solid #ced4da;
      border-radius: 6px;
      background: white;
      cursor: pointer;
      min-width: 150px;

      &:focus {
        outline: none;
        border-color: #007bff;
      }
    }
  }

  .view-controls {
    display: flex;
    align-items: center;
    gap: 20px;

    @media (max-width: 768px) {
      justify-content: space-between;
      width: 100%;
    }

    .view-toggle {
      display: flex;
      background: #f8f9fa;
      border-radius: 6px;
      padding: 4px;

      .view-btn {
        background: none;
        border: none;
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
        color: #6c757d;

        &:hover {
          background: #e9ecef;
        }

        &.active {
          background: #007bff;
          color: white;
        }
      }
    }

    .quote-summary {
      display: flex;
      align-items: center;
      gap: 12px;

      .quote-count {
        font-size: 0.9rem;
        color: #6c757d;
      }

      .quote-btn {
        background: #28a745;
        color: white;
        text-decoration: none;
        padding: 8px 16px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        transition: background 0.2s;

        &:hover {
          background: #218838;
        }
      }
    }
  }
}

// Product Views
.products-container {
  .no-products {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;

    i {
      font-size: 3rem;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    h3 {
      margin-bottom: 8px;
    }
  }

  .products-card-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
  }

  .products-grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }

  .products-list-view {
    // Styles handled in ProductList component
  }
}

// Quote Summary Styles
.quote-summary {
  .quote-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;

    .quote-info {
      h1 {
        margin-bottom: 8px;
        color: #2c3e50;
      }

      .quote-meta {
        display: flex;
        gap: 24px;
        color: #6c757d;
        font-size: 0.9rem;
      }
    }

    .quote-actions {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }
  }

  .customer-section,
  .quote-items-section {
    background: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;

    h3 {
      margin-bottom: 20px;
      color: #2c3e50;
      border-bottom: 2px solid #f8f9fa;
      padding-bottom: 8px;
    }
  }

  .customer-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;

    .info-row {
      display: flex;
      gap: 8px;

      .label {
        font-weight: 600;
        color: #495057;
        min-width: 60px;
      }
    }
  }

  .empty-quote {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;

    i {
      font-size: 3rem;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    h4 {
      margin-bottom: 8px;
    }

    p {
      margin-bottom: 20px;
    }
  }

  .quote-items {
    .items-header {
      display: grid;
      grid-template-columns: 2fr 120px 120px 120px 80px;
      gap: 16px;
      padding: 16px 0;
      border-bottom: 2px solid #dee2e6;
      font-weight: 600;
      color: #495057;
      text-transform: uppercase;
      font-size: 0.8rem;
      letter-spacing: 0.5px;
    }

    .quote-item {
      display: grid;
      grid-template-columns: 2fr 120px 120px 120px 80px;
      gap: 16px;
      padding: 20px 0;
      border-bottom: 1px solid #dee2e6;
      align-items: center;

      .item-info {
        display: flex;
        gap: 16px;
        align-items: center;

        .item-image {
          width: 60px;
          height: 60px;
          object-fit: cover;
          border-radius: 6px;
        }

        .item-details {
          .item-name {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 4px;
            color: #2c3e50;
          }

          .item-brand {
            color: #3498db;
            font-weight: 500;
            margin-bottom: 2px;
          }

          .item-part {
            font-size: 0.8rem;
            color: #6c757d;
          }
        }
      }

      .quantity-controls {
        display: flex;
        align-items: center;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        overflow: hidden;

        .qty-btn {
          background: #f8f9fa;
          border: none;
          padding: 6px 8px;
          cursor: pointer;

          &:hover:not(:disabled) {
            background: #e9ecef;
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }

        .qty-input {
          border: none;
          width: 50px;
          text-align: center;
          padding: 6px 4px;

          &:focus {
            outline: none;
          }
        }
      }

      .remove-btn {
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 6px 8px;
        cursor: pointer;

        &:hover {
          background: #c82333;
        }
      }
    }
  }

  .quote-totals {
    background: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: 24px;

    .totals-section {
      .total-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        min-width: 250px;

        &.grand-total {
          border-top: 2px solid #dee2e6;
          padding-top: 8px;
          margin-top: 8px;
          font-weight: 700;
          font-size: 1.2rem;
          color: #28a745;
        }

        .total-label {
          font-weight: 500;
        }

        .total-value {
          font-weight: 600;
        }
      }
    }

    .quote-footer-actions {
      display: flex;
      gap: 12px;
    }
  }
}

// Button Styles
.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  font-size: 0.9rem;

  &.btn-primary {
    background: #007bff;
    color: white;

    &:hover {
      background: #0056b3;
    }
  }

  &.btn-secondary {
    background: #6c757d;
    color: white;

    &:hover {
      background: #5a6268;
    }
  }

  &.btn-success {
    background: #28a745;
    color: white;

    &:hover {
      background: #218838;
    }
  }

  &.btn-danger {
    background: #dc3545;
    color: white;

    &:hover {
      background: #c82333;
    }
  }

  &.btn-outline {
    background: transparent;
    color: #007bff;
    border: 1px solid #007bff;

    &:hover {
      background: #007bff;
      color: white;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .app-main {
    padding: 1rem 0;
  }

  .products-card-view {
    grid-template-columns: 1fr;
  }

  .products-grid-view {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
  }

  .quote-items {
    .items-header,
    .quote-item {
      grid-template-columns: 1fr;
      gap: 8px;
    }

    .quote-item {
      .item-info {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
      }
    }
  }

  .quote-totals {
    flex-direction: column;
    align-items: stretch;

    .quote-footer-actions {
      justify-content: center;
    }
  }
}
