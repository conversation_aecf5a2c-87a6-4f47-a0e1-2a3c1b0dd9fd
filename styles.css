/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #2c3e50;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.5rem;
    font-weight: 700;
}

.logo i {
    font-size: 2rem;
    color: #ffd700;
}

.nav {
    display: flex;
    gap: 24px;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Main Content */
.main {
    padding: 2rem 0;
}

.section {
    display: none;
}

.section.active {
    display: block;
}

/* Controls */
.controls {
    background: white;
    padding: 24px;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.search-filters {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    min-width: 280px;
}

.search-box i {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 1.1rem;
}

.search-box input {
    width: 100%;
    padding: 12px 16px 12px 48px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

select {
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    background: white;
    cursor: pointer;
    font-size: 0.95rem;
    min-width: 160px;
    transition: all 0.3s ease;
}

select:focus {
    outline: none;
    border-color: #667eea;
}

.view-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.view-toggle {
    display: flex;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 4px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.view-btn {
    background: none;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.view-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.view-btn.active {
    background: #667eea;
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.quote-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(40, 167, 69, 0.3);
}

.quote-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* Products Container */
.products-container {
    min-height: 400px;
}

/* Card View */
.products-container.card-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 24px;
}

/* List View */
.products-container.list-view {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Grid View */
.products-container.grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

/* Product Card */
.product-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.product-image {
    position: relative;
    height: 220px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.discount-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.out-of-stock-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
}

.product-content {
    padding: 24px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
}

.brand-category {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
}

.brand {
    color: #667eea;
    font-weight: 600;
}

.category {
    color: #6c757d;
    font-size: 0.9rem;
}

.product-description {
    color: #5a6c7d;
    margin-bottom: 16px;
    line-height: 1.5;
}

.features {
    margin-bottom: 16px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
    font-size: 0.9rem;
    color: #5a6c7d;
}

.feature-item i {
    color: #28a745;
    font-size: 0.8rem;
}

.compatibility {
    margin-bottom: 16px;
}

.compatibility-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 6px;
    display: block;
}

.vehicle-types {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.vehicle-type {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    padding: 4px 10px;
    border-radius: 16px;
    font-size: 0.8rem;
    font-weight: 500;
}

.rating-section {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.stars {
    display: flex;
    gap: 2px;
}

.star {
    color: #ddd;
    font-size: 1rem;
}

.star.filled {
    color: #ffd700;
}

.rating-text {
    font-size: 0.9rem;
    color: #6c757d;
}

.pricing {
    margin-bottom: 20px;
}

.original-price {
    text-decoration: line-through;
    color: #6c757d;
    font-size: 0.9rem;
}

.current-price {
    font-size: 1.8rem;
    font-weight: 700;
    color: #28a745;
}

.product-actions {
    margin-top: auto;
    display: flex;
    gap: 12px;
    align-items: center;
}

.quantity-selector {
    display: flex;
    align-items: center;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
}

.qty-btn {
    background: #f8f9fa;
    border: none;
    padding: 10px 12px;
    cursor: pointer;
    transition: background 0.2s;
    font-weight: 600;
}

.qty-btn:hover:not(:disabled) {
    background: #e9ecef;
}

.qty-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.qty-input {
    border: none;
    width: 60px;
    text-align: center;
    padding: 10px 8px;
    font-weight: 600;
    font-size: 1rem;
}

.qty-input:focus {
    outline: none;
}

.add-to-quote-btn {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.add-to-quote-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.add-to-quote-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Product List View */
.list-header {
    display: grid;
    grid-template-columns: 80px 2fr 120px 140px 140px 120px 120px 180px;
    background: #f8f9fa;
    padding: 16px;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

.list-item {
    display: grid;
    grid-template-columns: 80px 2fr 120px 140px 140px 120px 120px 180px;
    padding: 16px;
    border-bottom: 1px solid #dee2e6;
    align-items: center;
    transition: background 0.2s;
}

.list-item:hover {
    background: #f8f9fa;
}

.list-item.out-of-stock {
    opacity: 0.6;
}

.list-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
}

.list-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.list-product-info h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 4px;
    color: #2c3e50;
}

.list-product-info p {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 4px;
}

/* Product Grid View */
.product-grid-item {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-grid-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.grid-image {
    height: 180px;
    overflow: hidden;
    position: relative;
}

.grid-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.grid-content {
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.grid-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
}

.grid-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: #28a745;
    margin-bottom: 12px;
}

.grid-actions {
    margin-top: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.grid-qty-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.grid-qty-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 0.8rem;
}

.grid-qty-display {
    font-weight: 600;
    min-width: 24px;
    text-align: center;
}

.grid-add-btn {
    background: #28a745;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.grid-add-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
}

/* Quote Section */
.quote-header {
    background: white;
    padding: 32px;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 24px;
}

.quote-info h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 8px;
}

.quote-meta {
    display: flex;
    gap: 24px;
    color: #6c757d;
    font-size: 1rem;
}

.quote-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 0.95rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(220, 53, 69, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

/* Customer Section */
.customer-section,
.quote-items-section {
    background: white;
    padding: 32px;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 32px;
}

.customer-section h3,
.quote-items-section h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 24px;
    border-bottom: 3px solid #f8f9fa;
    padding-bottom: 12px;
}

.customer-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.customer-detail {
    display: flex;
    gap: 12px;
}

.customer-detail .label {
    font-weight: 600;
    color: #495057;
    min-width: 80px;
}

/* Empty Quote */
.empty-quote {
    text-align: center;
    padding: 80px 20px;
    color: #6c757d;
}

.empty-quote i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-quote h4 {
    font-size: 1.5rem;
    margin-bottom: 12px;
    color: #495057;
}

.empty-quote p {
    font-size: 1.1rem;
    margin-bottom: 24px;
}

/* Quote Totals */
.quote-totals {
    background: white;
    padding: 32px;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: 32px;
}

.totals-section {
    min-width: 300px;
}

.total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    font-size: 1.1rem;
}

.total-row.grand-total {
    border-top: 3px solid #dee2e6;
    padding-top: 16px;
    margin-top: 16px;
    font-weight: 700;
    font-size: 1.4rem;
    color: #28a745;
}

.quote-footer-actions {
    display: flex;
    gap: 16px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 16px;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px;
    border-bottom: 2px solid #f8f9fa;
}

.modal-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6c757d;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s;
}

.close-btn:hover {
    background: #f8f9fa;
    color: #495057;
}

/* Form Styles */
.customer-form {
    padding: 32px;
}

.form-section {
    margin-bottom: 32px;
}

.form-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
}

.form-group input,
.form-group select {
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 2px solid #f8f9fa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 16px;
    }
    
    .controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-filters {
        justify-content: center;
        margin-bottom: 16px;
    }
    
    .view-controls {
        justify-content: space-between;
    }
    
    .products-container.card-view {
        grid-template-columns: 1fr;
    }
    
    .products-container.grid-view {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
    
    .quote-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .quote-totals {
        flex-direction: column;
        align-items: stretch;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .list-header,
    .list-item {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .list-item {
        padding: 12px;
    }
}

/* Loading Animation */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-size: 1.2rem;
    color: #6c757d;
}

.loading i {
    animation: spin 1s linear infinite;
    margin-right: 12px;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Success Animation */
.success-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 16px 24px;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(40, 167, 69, 0.3);
    z-index: 1001;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Quote Items Styles */
.items-header {
    display: grid;
    grid-template-columns: 2fr 120px 120px 120px 80px;
    gap: 16px;
    padding: 16px 0;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

.quote-item {
    display: grid;
    grid-template-columns: 2fr 120px 120px 120px 80px;
    gap: 16px;
    padding: 20px 0;
    border-bottom: 1px solid #dee2e6;
    align-items: center;
}

.item-info {
    display: flex;
    gap: 16px;
    align-items: center;
}

.item-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
}

.item-details h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 4px;
    color: #2c3e50;
}

.item-details p {
    color: #667eea;
    font-weight: 500;
    margin-bottom: 2px;
}

.item-details small {
    font-size: 0.8rem;
    color: #6c757d;
}

.unit-price,
.item-total {
    font-weight: 600;
    color: #28a745;
    font-size: 1.1rem;
}

.remove-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 10px;
    cursor: pointer;
    transition: all 0.2s;
}

.remove-btn:hover {
    background: #c82333;
    transform: scale(1.05);
}

/* Product Meta Styles */
.product-meta {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f8f9fa;
    font-size: 0.85rem;
    color: #6c757d;
}

.product-meta div {
    margin-bottom: 4px;
}

.stock-info.low-stock {
    color: #fd7e14;
    font-weight: 600;
}

/* Quick View Styles */
.quick-view {
    max-width: 800px;
    width: 90%;
}

.quick-view-body {
    padding: 32px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
}

.quick-view-image {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 12px;
}

.quick-view-info h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: #2c3e50;
}

.quick-view-info .brand {
    color: #667eea;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 16px;
}

.quick-view-info .description {
    color: #5a6c7d;
    line-height: 1.6;
    margin-bottom: 20px;
}

.quick-view-info .features h4 {
    font-size: 1.1rem;
    margin-bottom: 12px;
    color: #495057;
}

.quick-view-info .features ul {
    margin: 0;
    padding-left: 20px;
}

.quick-view-info .features li {
    margin-bottom: 6px;
    color: #5a6c7d;
}

.quick-view-info .warranty,
.quick-view-info .part-number {
    margin-top: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    font-weight: 500;
}

/* Empty Products Styles */
.empty-products {
    text-align: center;
    padding: 80px 20px;
    color: #6c757d;
    grid-column: 1 / -1;
}

.empty-products i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-products h3 {
    font-size: 1.8rem;
    margin-bottom: 12px;
    color: #495057;
}

.empty-products p {
    font-size: 1.1rem;
}
