<template>
  <div class="product-list">
    <div class="list-header">
      <div class="header-cell image-col">Image</div>
      <div class="header-cell name-col">Product</div>
      <div class="header-cell brand-col">Brand</div>
      <div class="header-cell category-col">Category</div>
      <div class="header-cell compatibility-col">Compatibility</div>
      <div class="header-cell rating-col">Rating</div>
      <div class="header-cell price-col">Price</div>
      <div class="header-cell stock-col">Stock</div>
      <div class="header-cell actions-col">Actions</div>
    </div>
    
    <div class="list-body">
      <div 
        v-for="product in products" 
        :key="product.id" 
        class="list-row"
        :class="{ 'out-of-stock': !product.inStock }"
      >
        <div class="list-cell image-col">
          <div class="product-image">
            <img :src="product.image" :alt="product.name" />
            <div v-if="!product.inStock" class="out-of-stock-overlay">
              <i class="fas fa-times"></i>
            </div>
          </div>
        </div>
        
        <div class="list-cell name-col">
          <div class="product-info">
            <h4 class="product-name">{{ product.name }}</h4>
            <p class="product-description">{{ product.description }}</p>
            <div class="part-number">Part #: {{ product.partNumber }}</div>
          </div>
        </div>
        
        <div class="list-cell brand-col">
          <span class="brand">{{ product.brand }}</span>
        </div>
        
        <div class="list-cell category-col">
          <span class="category">{{ product.category }}</span>
        </div>
        
        <div class="list-cell compatibility-col">
          <div class="vehicle-types">
            <span 
              v-for="type in product.compatibility.slice(0, 2)" 
              :key="type" 
              class="vehicle-type"
            >
              {{ type }}
            </span>
            <span v-if="product.compatibility.length > 2" class="more-types">
              +{{ product.compatibility.length - 2 }} more
            </span>
          </div>
        </div>
        
        <div class="list-cell rating-col">
          <div class="rating">
            <div class="stars">
              <i 
                v-for="star in 5" 
                :key="star"
                class="fas fa-star"
                :class="{ filled: star <= Math.floor(product.rating) }"
              ></i>
            </div>
            <span class="rating-value">{{ product.rating }}</span>
            <span class="reviews-count">({{ product.reviews }})</span>
          </div>
        </div>
        
        <div class="list-cell price-col">
          <div class="pricing">
            <div v-if="product.originalPrice > product.price" class="original-price">
              ${{ product.originalPrice.toFixed(2) }}
            </div>
            <div class="current-price">${{ product.price.toFixed(2) }}</div>
            <div v-if="product.originalPrice > product.price" class="discount">
              {{ Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100) }}% OFF
            </div>
          </div>
        </div>
        
        <div class="list-cell stock-col">
          <div class="stock-info" :class="{ 'low-stock': product.stockQuantity < 10 }">
            <i class="fas" :class="product.inStock ? 'fa-check-circle' : 'fa-times-circle'"></i>
            {{ product.inStock ? `${product.stockQuantity} available` : 'Out of stock' }}
          </div>
        </div>
        
        <div class="list-cell actions-col">
          <div class="actions">
            <div class="quantity-selector">
              <button 
                @click="decrementQuantity(product.id)" 
                :disabled="getQuantity(product.id) <= 1"
                class="qty-btn"
              >
                <i class="fas fa-minus"></i>
              </button>
              <input 
                :value="getQuantity(product.id)"
                @input="updateQuantity(product.id, $event.target.value)"
                type="number" 
                min="1" 
                max="99"
                class="qty-input"
              >
              <button 
                @click="incrementQuantity(product.id)" 
                :disabled="getQuantity(product.id) >= 99"
                class="qty-btn"
              >
                <i class="fas fa-plus"></i>
              </button>
            </div>
            
            <button 
              @click="addToQuote(product)" 
              :disabled="!product.inStock"
              class="add-to-quote-btn"
            >
              <i class="fas fa-plus"></i>
              Add
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const props = defineProps({
  products: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['add-to-quote'])

// Track quantities for each product
const quantities = reactive({})

// Initialize quantities
props.products.forEach(product => {
  if (!quantities[product.id]) {
    quantities[product.id] = 1
  }
})

function getQuantity(productId) {
  return quantities[productId] || 1
}

function updateQuantity(productId, value) {
  const qty = parseInt(value) || 1
  quantities[productId] = Math.max(1, Math.min(99, qty))
}

function incrementQuantity(productId) {
  if (quantities[productId] < 99) {
    quantities[productId]++
  }
}

function decrementQuantity(productId) {
  if (quantities[productId] > 1) {
    quantities[productId]--
  }
}

function addToQuote(product) {
  if (product.inStock) {
    emit('add-to-quote', product, quantities[product.id])
    quantities[product.id] = 1 // Reset quantity after adding
  }
}
</script>

<style lang="scss" scoped>
.product-list {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.list-header {
  display: grid;
  grid-template-columns: 80px 2fr 120px 140px 140px 120px 120px 120px 180px;
  background: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;

  .header-cell {
    padding: 16px 12px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.list-body {
  .list-row {
    display: grid;
    grid-template-columns: 80px 2fr 120px 140px 140px 120px 120px 120px 180px;
    border-bottom: 1px solid #dee2e6;
    transition: background 0.2s;

    &:hover {
      background: #f8f9fa;
    }

    &.out-of-stock {
      opacity: 0.6;
    }

    .list-cell {
      padding: 16px 12px;
      display: flex;
      align-items: center;
    }
  }
}

.product-image {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .out-of-stock-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }
}

.product-info {
  .product-name {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #2c3e50;
  }

  .product-description {
    font-size: 0.8rem;
    color: #6c757d;
    margin: 0 0 4px 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .part-number {
    font-size: 0.75rem;
    color: #adb5bd;
  }
}

.brand {
  font-weight: 500;
  color: #3498db;
}

.category {
  font-size: 0.9rem;
  color: #6c757d;
}

.vehicle-types {
  display: flex;
  flex-direction: column;
  gap: 2px;

  .vehicle-type {
    background: #e9ecef;
    color: #495057;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75rem;
    width: fit-content;
  }

  .more-types {
    font-size: 0.7rem;
    color: #6c757d;
    font-style: italic;
  }
}

.rating {
  display: flex;
  flex-direction: column;
  gap: 2px;

  .stars {
    display: flex;
    gap: 1px;

    .fa-star {
      color: #dee2e6;
      font-size: 0.7rem;

      &.filled {
        color: #ffc107;
      }
    }
  }

  .rating-value {
    font-size: 0.8rem;
    font-weight: 500;
  }

  .reviews-count {
    font-size: 0.7rem;
    color: #6c757d;
  }
}

.pricing {
  .original-price {
    text-decoration: line-through;
    color: #6c757d;
    font-size: 0.8rem;
  }

  .current-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: #28a745;
  }

  .discount {
    font-size: 0.7rem;
    color: #dc3545;
    font-weight: 500;
  }
}

.stock-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;

  .fa-check-circle {
    color: #28a745;
  }

  .fa-times-circle {
    color: #dc3545;
  }

  &.low-stock {
    color: #fd7e14;
  }
}

.actions {
  display: flex;
  gap: 8px;
  align-items: center;

  .quantity-selector {
    display: flex;
    align-items: center;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    overflow: hidden;

    .qty-btn {
      background: #f8f9fa;
      border: none;
      padding: 4px 6px;
      cursor: pointer;
      font-size: 0.8rem;

      &:hover:not(:disabled) {
        background: #e9ecef;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .qty-input {
      border: none;
      width: 35px;
      text-align: center;
      padding: 4px 2px;
      font-size: 0.8rem;

      &:focus {
        outline: none;
      }
    }
  }

  .add-to-quote-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;

    &:hover:not(:disabled) {
      background: #0056b3;
    }

    &:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }
  }
}

@media (max-width: 1200px) {
  .list-header,
  .list-row {
    grid-template-columns: 60px 1.5fr 100px 120px 120px 100px 100px 100px 150px;
  }
}

@media (max-width: 768px) {
  .product-list {
    overflow-x: auto;
  }
  
  .list-header,
  .list-row {
    min-width: 1000px;
  }
}
</style>
