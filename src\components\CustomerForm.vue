<template>
  <div class="customer-form-overlay" @click="$emit('close')">
    <div class="customer-form-modal" @click.stop>
      <div class="modal-header">
        <h3>Customer Information</h3>
        <button @click="$emit('close')" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <form @submit.prevent="handleSubmit" class="customer-form">
        <div class="form-section">
          <h4>Contact Information</h4>
          <div class="form-row">
            <div class="form-group">
              <label for="name">Full Name *</label>
              <input 
                id="name"
                v-model="formData.name" 
                type="text" 
                required
                placeholder="Enter full name"
              >
            </div>
            <div class="form-group">
              <label for="email">Email Address *</label>
              <input 
                id="email"
                v-model="formData.email" 
                type="email" 
                required
                placeholder="Enter email address"
              >
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="phone">Phone Number</label>
              <input 
                id="phone"
                v-model="formData.phone" 
                type="tel" 
                placeholder="Enter phone number"
              >
            </div>
          </div>
        </div>
        
        <div class="form-section">
          <h4>Vehicle Information</h4>
          <div class="form-row">
            <div class="form-group">
              <label for="vehicleYear">Year</label>
              <select id="vehicleYear" v-model="formData.vehicleYear">
                <option value="">Select Year</option>
                <option v-for="year in years" :key="year" :value="year">
                  {{ year }}
                </option>
              </select>
            </div>
            <div class="form-group">
              <label for="vehicleMake">Make</label>
              <input 
                id="vehicleMake"
                v-model="formData.vehicleMake" 
                type="text" 
                placeholder="e.g., Toyota, Honda, Ford"
              >
            </div>
            <div class="form-group">
              <label for="vehicleModel">Model</label>
              <input 
                id="vehicleModel"
                v-model="formData.vehicleModel" 
                type="text" 
                placeholder="e.g., Camry, Civic, F-150"
              >
            </div>
          </div>
        </div>
        
        <div class="form-actions">
          <button type="button" @click="$emit('close')" class="btn btn-secondary">
            Cancel
          </button>
          <button type="submit" class="btn btn-primary">
            Save Information
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useQuotationStore } from '../stores/quotation'

const emit = defineEmits(['close', 'save'])
const quotationStore = useQuotationStore()

const formData = ref({
  name: '',
  email: '',
  phone: '',
  vehicleYear: '',
  vehicleMake: '',
  vehicleModel: ''
})

const years = ref([])

// Generate years from current year back to 1990
function generateYears() {
  const currentYear = new Date().getFullYear()
  const yearList = []
  for (let year = currentYear; year >= 1990; year--) {
    yearList.push(year)
  }
  return yearList
}

function handleSubmit() {
  emit('save', { ...formData.value })
}

onMounted(() => {
  years.value = generateYears()
  // Pre-populate form with existing customer info
  formData.value = { ...quotationStore.customerInfo }
})
</script>

<style lang="scss" scoped>
.customer-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.customer-form-modal {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #dee2e6;

  h3 {
    margin: 0;
    color: #2c3e50;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #6c757d;
    padding: 4px;

    &:hover {
      color: #495057;
    }
  }
}

.customer-form {
  padding: 24px;
}

.form-section {
  margin-bottom: 24px;

  h4 {
    margin: 0 0 16px 0;
    color: #495057;
    font-size: 1.1rem;
    font-weight: 600;
  }
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.form-group {
  display: flex;
  flex-direction: column;

  label {
    margin-bottom: 6px;
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
  }

  input,
  select {
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.2s, box-shadow 0.2s;

    &:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    &::placeholder {
      color: #6c757d;
    }
  }

  select {
    cursor: pointer;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;

  .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;

    &.btn-secondary {
      background: #6c757d;
      color: white;

      &:hover {
        background: #5a6268;
      }
    }

    &.btn-primary {
      background: #007bff;
      color: white;

      &:hover {
        background: #0056b3;
      }
    }
  }
}
</style>
