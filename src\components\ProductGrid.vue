<template>
  <div class="product-grid-item" :class="{ 'out-of-stock': !product.inStock }">
    <div class="grid-image">
      <img :src="product.image" :alt="product.name" />
      <div v-if="!product.inStock" class="out-of-stock-overlay">
        <span>Out of Stock</span>
      </div>
      <div v-if="product.originalPrice > product.price" class="discount-badge">
        {{ Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100) }}% OFF
      </div>
      <div class="quick-actions">
        <button class="quick-view-btn" @click="showQuickView = true">
          <i class="fas fa-eye"></i>
        </button>
      </div>
    </div>
    
    <div class="grid-content">
      <div class="product-header">
        <h4 class="product-name">{{ product.name }}</h4>
        <div class="brand">{{ product.brand }}</div>
      </div>
      
      <div class="rating-price">
        <div class="rating">
          <div class="stars">
            <i 
              v-for="star in 5" 
              :key="star"
              class="fas fa-star"
              :class="{ filled: star <= Math.floor(product.rating) }"
            ></i>
          </div>
          <span class="rating-value">{{ product.rating }}</span>
        </div>
        
        <div class="pricing">
          <div v-if="product.originalPrice > product.price" class="original-price">
            ${{ product.originalPrice.toFixed(2) }}
          </div>
          <div class="current-price">${{ product.price.toFixed(2) }}</div>
        </div>
      </div>
      
      <div class="compatibility-tags">
        <span 
          v-for="type in product.compatibility.slice(0, 3)" 
          :key="type" 
          class="compatibility-tag"
        >
          {{ type }}
        </span>
      </div>
      
      <div class="grid-actions">
        <div class="quantity-controls">
          <button 
            @click="decrementQuantity" 
            :disabled="quantity <= 1"
            class="qty-btn"
          >
            <i class="fas fa-minus"></i>
          </button>
          <span class="qty-display">{{ quantity }}</span>
          <button 
            @click="incrementQuantity" 
            :disabled="quantity >= 99"
            class="qty-btn"
          >
            <i class="fas fa-plus"></i>
          </button>
        </div>
        
        <button 
          @click="addToQuote" 
          :disabled="!product.inStock"
          class="add-btn"
        >
          <i class="fas fa-cart-plus"></i>
        </button>
      </div>
      
      <div class="stock-status" :class="{ 'low-stock': product.stockQuantity < 10 }">
        <i class="fas" :class="product.inStock ? 'fa-check' : 'fa-times'"></i>
        {{ product.inStock ? `${product.stockQuantity} in stock` : 'Out of stock' }}
      </div>
    </div>

    <!-- Quick View Modal (simplified) -->
    <div v-if="showQuickView" class="quick-view-overlay" @click="showQuickView = false">
      <div class="quick-view-modal" @click.stop>
        <button class="close-btn" @click="showQuickView = false">
          <i class="fas fa-times"></i>
        </button>
        <div class="modal-content">
          <img :src="product.image" :alt="product.name" class="modal-image" />
          <div class="modal-info">
            <h3>{{ product.name }}</h3>
            <p class="brand">{{ product.brand }}</p>
            <p class="description">{{ product.description }}</p>
            <div class="features">
              <h4>Features:</h4>
              <ul>
                <li v-for="feature in product.features" :key="feature">{{ feature }}</li>
              </ul>
            </div>
            <div class="warranty">Warranty: {{ product.warranty }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  product: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['add-to-quote'])

const quantity = ref(1)
const showQuickView = ref(false)

function incrementQuantity() {
  if (quantity.value < 99) {
    quantity.value++
  }
}

function decrementQuantity() {
  if (quantity.value > 1) {
    quantity.value--
  }
}

function addToQuote() {
  if (props.product.inStock) {
    emit('add-to-quote', props.product, quantity.value)
    quantity.value = 1 // Reset quantity after adding
  }
}
</script>

<style lang="scss" scoped>
.product-grid-item {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &.out-of-stock {
    opacity: 0.7;
  }
}

.grid-image {
  position: relative;
  height: 160px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  &:hover img {
    transform: scale(1.05);
  }

  .out-of-stock-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
  }

  .discount-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #dc3545;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: 600;
  }

  .quick-actions {
    position: absolute;
    top: 8px;
    left: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;

    .quick-view-btn {
      background: rgba(255, 255, 255, 0.9);
      border: none;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: background 0.2s;

      &:hover {
        background: white;
      }
    }
  }

  &:hover .quick-actions {
    opacity: 1;
  }
}

.grid-content {
  padding: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-header {
  margin-bottom: 8px;

  .product-name {
    font-size: 0.95rem;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #2c3e50;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .brand {
    font-size: 0.8rem;
    color: #3498db;
    font-weight: 500;
  }
}

.rating-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .rating {
    display: flex;
    align-items: center;
    gap: 4px;

    .stars {
      display: flex;
      gap: 1px;

      .fa-star {
        color: #dee2e6;
        font-size: 0.7rem;

        &.filled {
          color: #ffc107;
        }
      }
    }

    .rating-value {
      font-size: 0.75rem;
      font-weight: 500;
      color: #495057;
    }
  }

  .pricing {
    text-align: right;

    .original-price {
      text-decoration: line-through;
      color: #6c757d;
      font-size: 0.7rem;
    }

    .current-price {
      font-size: 1rem;
      font-weight: 700;
      color: #28a745;
    }
  }
}

.compatibility-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 12px;

  .compatibility-tag {
    background: #e9ecef;
    color: #495057;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7rem;
  }
}

.grid-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  margin-bottom: 8px;

  .quantity-controls {
    display: flex;
    align-items: center;
    gap: 8px;

    .qty-btn {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 0.7rem;

      &:hover:not(:disabled) {
        background: #e9ecef;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .qty-display {
      font-weight: 600;
      min-width: 20px;
      text-align: center;
      font-size: 0.9rem;
    }
  }

  .add-btn {
    background: #28a745;
    color: white;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.2s;

    &:hover:not(:disabled) {
      background: #218838;
    }

    &:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }
  }
}

.stock-status {
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6c757d;

  .fa-check {
    color: #28a745;
  }

  .fa-times {
    color: #dc3545;
  }

  &.low-stock {
    color: #fd7e14;
  }
}

// Quick View Modal
.quick-view-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.quick-view-modal {
  background: white;
  border-radius: 8px;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;

  .close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    z-index: 1001;
  }

  .modal-content {
    padding: 20px;

    .modal-image {
      width: 100%;
      height: 200px;
      object-fit: cover;
      border-radius: 6px;
      margin-bottom: 16px;
    }

    .modal-info {
      h3 {
        margin: 0 0 8px 0;
        color: #2c3e50;
      }

      .brand {
        color: #3498db;
        font-weight: 500;
        margin-bottom: 12px;
      }

      .description {
        color: #5a6c7d;
        line-height: 1.5;
        margin-bottom: 16px;
      }

      .features {
        h4 {
          margin: 0 0 8px 0;
          font-size: 1rem;
        }

        ul {
          margin: 0;
          padding-left: 20px;

          li {
            margin-bottom: 4px;
            color: #5a6c7d;
          }
        }
      }

      .warranty {
        margin-top: 12px;
        font-size: 0.9rem;
        color: #7f8c8d;
      }
    }
  }
}
</style>
