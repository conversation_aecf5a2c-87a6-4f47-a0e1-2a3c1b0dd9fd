<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoParts Pro - Vehicle Aftermarket Quotation System</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-car-side"></i>
                <span>AutoParts Pro</span>
            </div>
            <nav class="nav">
                <a href="#products" class="nav-link active" onclick="showProducts()">Products</a>
                <a href="#quote" class="nav-link" onclick="showQuote()">Quote (<span id="quote-count">0</span>)</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Products Section -->
        <section id="products" class="section active">
            <div class="container">
                <!-- Controls -->
                <div class="controls">
                    <div class="search-filters">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="search-input" placeholder="Search products..." oninput="filterProducts()">
                        </div>
                        <select id="category-filter" onchange="filterProducts()">
                            <option value="">All Categories</option>
                            <option value="performance">Performance Parts</option>
                            <option value="exterior">Exterior Accessories</option>
                            <option value="interior">Interior Accessories</option>
                            <option value="lighting">Lighting</option>
                            <option value="audio">Audio & Electronics</option>
                            <option value="wheels">Wheels & Tires</option>
                            <option value="suspension">Suspension</option>
                            <option value="exhaust">Exhaust Systems</option>
                        </select>
                        <select id="vehicle-filter" onchange="filterProducts()">
                            <option value="">All Vehicles</option>
                            <option value="sedan">Sedan</option>
                            <option value="suv">SUV</option>
                            <option value="truck">Truck</option>
                            <option value="coupe">Coupe</option>
                            <option value="hatchback">Hatchback</option>
                        </select>
                    </div>
                    
                    <div class="view-controls">
                        <div class="view-toggle">
                            <button class="view-btn active" data-view="card" onclick="switchView('card')">
                                <i class="fas fa-th-large"></i>
                                <span>Card</span>
                            </button>
                            <button class="view-btn" data-view="list" onclick="switchView('list')">
                                <i class="fas fa-list"></i>
                                <span>List</span>
                            </button>
                            <button class="view-btn" data-view="grid" onclick="switchView('grid')">
                                <i class="fas fa-th"></i>
                                <span>Grid</span>
                            </button>
                        </div>
                        <button class="quote-btn" onclick="showQuote()">
                            <i class="fas fa-file-invoice-dollar"></i>
                            View Quote ($<span id="quote-total">0.00</span>)
                        </button>
                    </div>
                </div>

                <!-- Products Container -->
                <div id="products-container" class="products-container card-view">
                    <!-- Products will be dynamically loaded here -->
                </div>
            </div>
        </section>

        <!-- Quote Section -->
        <section id="quote" class="section">
            <div class="container">
                <div class="quote-header">
                    <div class="quote-info">
                        <h1>Quotation Summary</h1>
                        <div class="quote-meta">
                            <span>Quote #<span id="quote-number"></span></span>
                            <span>Date: <span id="quote-date"></span></span>
                        </div>
                    </div>
                    <div class="quote-actions">
                        <button class="btn btn-secondary" onclick="showCustomerForm()">
                            <i class="fas fa-user"></i> Customer Info
                        </button>
                        <button class="btn btn-primary" onclick="exportPDF()">
                            <i class="fas fa-file-pdf"></i> Export PDF
                        </button>
                        <button class="btn btn-outline" onclick="printQuote()">
                            <i class="fas fa-print"></i> Print
                        </button>
                    </div>
                </div>

                <!-- Customer Info -->
                <div id="customer-info" class="customer-section" style="display: none;">
                    <h3>Customer Information</h3>
                    <div class="customer-details">
                        <!-- Customer details will be populated here -->
                    </div>
                </div>

                <!-- Quote Items -->
                <div class="quote-items-section">
                    <h3>Quote Items</h3>
                    <div id="quote-items" class="quote-items">
                        <div class="empty-quote">
                            <i class="fas fa-shopping-cart"></i>
                            <h4>No items in quote</h4>
                            <p>Add some products to get started</p>
                            <button class="btn btn-primary" onclick="showProducts()">
                                <i class="fas fa-plus"></i> Browse Products
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Quote Totals -->
                <div id="quote-totals" class="quote-totals" style="display: none;">
                    <div class="totals-section">
                        <div class="total-row">
                            <span>Subtotal:</span>
                            <span id="subtotal">$0.00</span>
                        </div>
                        <div class="total-row">
                            <span>Tax (8%):</span>
                            <span id="tax">$0.00</span>
                        </div>
                        <div class="total-row grand-total">
                            <span>Total:</span>
                            <span id="total">$0.00</span>
                        </div>
                    </div>
                    <div class="quote-footer-actions">
                        <button class="btn btn-danger" onclick="clearQuote()">
                            <i class="fas fa-trash"></i> Clear Quote
                        </button>
                        <button class="btn btn-success" onclick="sendQuote()">
                            <i class="fas fa-paper-plane"></i> Send Quote
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Customer Form Modal -->
    <div id="customer-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Customer Information</h3>
                <button class="close-btn" onclick="closeCustomerForm()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="customer-form" class="customer-form" onsubmit="saveCustomerInfo(event)">
                <div class="form-section">
                    <h4>Contact Information</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Full Name *</label>
                            <input type="text" name="name" required>
                        </div>
                        <div class="form-group">
                            <label>Email Address *</label>
                            <input type="email" name="email" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Phone Number</label>
                            <input type="tel" name="phone">
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <h4>Vehicle Information</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Year</label>
                            <select name="year">
                                <option value="">Select Year</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Make</label>
                            <input type="text" name="make" placeholder="e.g., Toyota, Honda, Ford">
                        </div>
                        <div class="form-group">
                            <label>Model</label>
                            <input type="text" name="model" placeholder="e.g., Camry, Civic, F-150">
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeCustomerForm()">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Information</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Product Quick View Modal -->
    <div id="quick-view-modal" class="modal">
        <div class="modal-content quick-view">
            <button class="close-btn" onclick="closeQuickView()">
                <i class="fas fa-times"></i>
            </button>
            <div id="quick-view-content">
                <!-- Quick view content will be populated here -->
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
