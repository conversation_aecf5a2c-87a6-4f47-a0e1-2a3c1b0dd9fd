// Global State
let currentView = 'card';
let quoteItems = [];
let customerInfo = {};
let filteredProducts = [];

// Sample Product Data
const products = [
    {
        id: 1,
        name: 'Cold Air Intake System',
        category: 'performance',
        brand: 'K&N',
        price: 299.99,
        originalPrice: 349.99,
        description: 'High-flow cold air intake system for improved performance and fuel efficiency',
        image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop',
        compatibility: ['sedan', 'coupe', 'hatchback'],
        inStock: true,
        stockQuantity: 25,
        rating: 4.5,
        reviews: 128,
        features: ['Increased horsepower', 'Better fuel economy', 'Easy installation'],
        warranty: '1 year',
        partNumber: 'KN-57-3510'
    },
    {
        id: 2,
        name: 'LED Headlight Kit',
        category: 'lighting',
        brand: 'Philips',
        price: 189.99,
        originalPrice: 229.99,
        description: 'Ultra-bright LED headlight conversion kit with 6000K color temperature',
        image: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=300&fit=crop',
        compatibility: ['sedan', 'suv', 'truck', 'coupe'],
        inStock: true,
        stockQuantity: 42,
        rating: 4.7,
        reviews: 89,
        features: ['6000K white light', '50,000 hour lifespan', 'Plug and play'],
        warranty: '3 years',
        partNumber: 'PH-LED-H11'
    },
    {
        id: 3,
        name: 'Carbon Fiber Spoiler',
        category: 'exterior',
        brand: 'Seibon',
        price: 459.99,
        originalPrice: 529.99,
        description: 'Lightweight carbon fiber rear spoiler for enhanced aerodynamics',
        image: 'https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=400&h=300&fit=crop',
        compatibility: ['sedan', 'coupe'],
        inStock: true,
        stockQuantity: 8,
        rating: 4.8,
        reviews: 34,
        features: ['Real carbon fiber', 'UV resistant coating', 'Professional installation recommended'],
        warranty: '2 years',
        partNumber: 'SB-CF-SP01'
    },
    {
        id: 4,
        name: 'Racing Seat Covers',
        category: 'interior',
        brand: 'Sparco',
        price: 129.99,
        originalPrice: 159.99,
        description: 'Premium racing-style seat covers with breathable fabric',
        image: 'https://images.unsplash.com/photo-1449965408869-eaa3f722e40d?w=400&h=300&fit=crop',
        compatibility: ['sedan', 'suv', 'truck', 'coupe', 'hatchback'],
        inStock: true,
        stockQuantity: 15,
        rating: 4.3,
        reviews: 67,
        features: ['Breathable fabric', 'Easy installation', 'Machine washable'],
        warranty: '1 year',
        partNumber: 'SP-SC-R01'
    },
    {
        id: 5,
        name: 'Performance Exhaust System',
        category: 'exhaust',
        brand: 'Borla',
        price: 899.99,
        originalPrice: 1099.99,
        description: 'Cat-back exhaust system with aggressive sound and performance gains',
        image: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=400&h=300&fit=crop',
        compatibility: ['sedan', 'coupe'],
        inStock: false,
        stockQuantity: 0,
        rating: 4.9,
        reviews: 156,
        features: ['Stainless steel construction', 'Increased horsepower', 'Aggressive sound'],
        warranty: '1 million mile warranty',
        partNumber: 'BR-140659'
    },
    {
        id: 6,
        name: 'Lowering Springs',
        category: 'suspension',
        brand: 'Eibach',
        price: 249.99,
        originalPrice: 299.99,
        description: 'Progressive rate lowering springs for improved handling and stance',
        image: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=400&h=300&fit=crop',
        compatibility: ['sedan', 'coupe', 'hatchback'],
        inStock: true,
        stockQuantity: 12,
        rating: 4.6,
        reviews: 93,
        features: ['1.5" front drop', '1.3" rear drop', 'Progressive rate design'],
        warranty: '1 year',
        partNumber: 'EB-PRO-KIT'
    },
    {
        id: 7,
        name: 'Premium Floor Mats',
        category: 'interior',
        brand: 'WeatherTech',
        price: 179.99,
        originalPrice: 199.99,
        description: 'All-weather floor mats with custom fit and superior protection',
        image: 'https://images.unsplash.com/photo-1449965408869-eaa3f722e40d?w=400&h=300&fit=crop',
        compatibility: ['sedan', 'suv', 'truck', 'coupe', 'hatchback'],
        inStock: true,
        stockQuantity: 35,
        rating: 4.4,
        reviews: 201,
        features: ['Custom fit', 'All-weather protection', 'Easy to clean'],
        warranty: '3 years',
        partNumber: 'WT-FM-001'
    },
    {
        id: 8,
        name: 'Alloy Wheel Set',
        category: 'wheels',
        brand: 'BBS',
        price: 1299.99,
        originalPrice: 1499.99,
        description: 'Lightweight forged alloy wheels for enhanced performance and style',
        image: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=400&h=300&fit=crop',
        compatibility: ['sedan', 'coupe', 'hatchback'],
        inStock: true,
        stockQuantity: 6,
        rating: 4.9,
        reviews: 45,
        features: ['Forged construction', 'Lightweight design', 'Multiple finishes available'],
        warranty: '2 years',
        partNumber: 'BBS-CH-R'
    }
];

// Initialize App
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    filteredProducts = [...products];
    generateQuoteNumber();
    setQuoteDate();
    populateYearDropdown();
    renderProducts();
    updateQuoteDisplay();
}

// Navigation Functions
function showProducts() {
    document.getElementById('products').classList.add('active');
    document.getElementById('quote').classList.remove('active');
    document.querySelector('.nav-link[onclick="showProducts()"]').classList.add('active');
    document.querySelector('.nav-link[onclick="showQuote()"]').classList.remove('active');
}

function showQuote() {
    document.getElementById('quote').classList.add('active');
    document.getElementById('products').classList.remove('active');
    document.querySelector('.nav-link[onclick="showQuote()"]').classList.add('active');
    document.querySelector('.nav-link[onclick="showProducts()"]').classList.remove('active');
    renderQuoteItems();
}

// View Switching
function switchView(view) {
    currentView = view;
    
    // Update button states
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`).classList.add('active');
    
    // Update container class
    const container = document.getElementById('products-container');
    container.className = `products-container ${view}-view`;
    
    // Re-render products
    renderProducts();
}

// Product Filtering
function filterProducts() {
    const searchQuery = document.getElementById('search-input').value.toLowerCase();
    const categoryFilter = document.getElementById('category-filter').value;
    const vehicleFilter = document.getElementById('vehicle-filter').value;
    
    filteredProducts = products.filter(product => {
        const matchesSearch = !searchQuery || 
            product.name.toLowerCase().includes(searchQuery) ||
            product.brand.toLowerCase().includes(searchQuery) ||
            product.description.toLowerCase().includes(searchQuery);
            
        const matchesCategory = !categoryFilter || product.category === categoryFilter;
        const matchesVehicle = !vehicleFilter || product.compatibility.includes(vehicleFilter);
        
        return matchesSearch && matchesCategory && matchesVehicle;
    });
    
    renderProducts();
}

// Product Rendering
function renderProducts() {
    const container = document.getElementById('products-container');
    
    if (filteredProducts.length === 0) {
        container.innerHTML = `
            <div class="empty-products">
                <i class="fas fa-search"></i>
                <h3>No products found</h3>
                <p>Try adjusting your search criteria</p>
            </div>
        `;
        return;
    }
    
    if (currentView === 'card') {
        renderCardView(container);
    } else if (currentView === 'list') {
        renderListView(container);
    } else if (currentView === 'grid') {
        renderGridView(container);
    }
}

function renderCardView(container) {
    container.innerHTML = filteredProducts.map(product => `
        <div class="product-card ${!product.inStock ? 'out-of-stock' : ''}">
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}">
                ${!product.inStock ? '<div class="out-of-stock-overlay">Out of Stock</div>' : ''}
                ${product.originalPrice > product.price ? `<div class="discount-badge">${Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF</div>` : ''}
            </div>
            <div class="product-content">
                <div class="product-header">
                    <h3>${product.name}</h3>
                    <div class="brand-category">
                        <span class="brand">${product.brand}</span>
                        <span class="category">${getCategoryName(product.category)}</span>
                    </div>
                </div>
                <p class="product-description">${product.description}</p>
                <div class="features">
                    ${product.features.slice(0, 2).map(feature => `
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            <span>${feature}</span>
                        </div>
                    `).join('')}
                </div>
                <div class="compatibility">
                    <span class="compatibility-label">Compatible with:</span>
                    <div class="vehicle-types">
                        ${product.compatibility.map(type => `<span class="vehicle-type">${type.charAt(0).toUpperCase() + type.slice(1)}</span>`).join('')}
                    </div>
                </div>
                <div class="rating-section">
                    <div class="stars">
                        ${generateStars(product.rating)}
                    </div>
                    <span class="rating-text">${product.rating} (${product.reviews} reviews)</span>
                </div>
                <div class="pricing">
                    ${product.originalPrice > product.price ? `<div class="original-price">$${product.originalPrice.toFixed(2)}</div>` : ''}
                    <div class="current-price">$${product.price.toFixed(2)}</div>
                </div>
                <div class="product-actions">
                    <div class="quantity-selector">
                        <button class="qty-btn" onclick="updateQuantity(${product.id}, -1)">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" class="qty-input" id="qty-${product.id}" value="1" min="1" max="99">
                        <button class="qty-btn" onclick="updateQuantity(${product.id}, 1)">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <button class="add-to-quote-btn" onclick="addToQuote(${product.id})" ${!product.inStock ? 'disabled' : ''}>
                        <i class="fas fa-plus"></i>
                        Add to Quote
                    </button>
                </div>
                <div class="product-meta">
                    <div>Part #: ${product.partNumber}</div>
                    <div>Warranty: ${product.warranty}</div>
                    <div class="stock-info ${product.stockQuantity < 10 ? 'low-stock' : ''}">
                        ${product.inStock ? `${product.stockQuantity} in stock` : 'Out of stock'}
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

function renderListView(container) {
    container.innerHTML = `
        <div class="list-header">
            <div>Image</div>
            <div>Product</div>
            <div>Brand</div>
            <div>Category</div>
            <div>Compatibility</div>
            <div>Rating</div>
            <div>Price</div>
            <div>Actions</div>
        </div>
        ${filteredProducts.map(product => `
            <div class="list-item ${!product.inStock ? 'out-of-stock' : ''}">
                <div class="list-image">
                    <img src="${product.image}" alt="${product.name}">
                </div>
                <div class="list-product-info">
                    <h4>${product.name}</h4>
                    <p>${product.description}</p>
                    <small>Part #: ${product.partNumber}</small>
                </div>
                <div class="brand">${product.brand}</div>
                <div>${getCategoryName(product.category)}</div>
                <div class="vehicle-types">
                    ${product.compatibility.slice(0, 2).map(type => `<span class="vehicle-type">${type.charAt(0).toUpperCase() + type.slice(1)}</span>`).join('')}
                    ${product.compatibility.length > 2 ? `<span class="more-types">+${product.compatibility.length - 2} more</span>` : ''}
                </div>
                <div class="rating">
                    <div class="stars">${generateStars(product.rating)}</div>
                    <span>${product.rating} (${product.reviews})</span>
                </div>
                <div class="pricing">
                    ${product.originalPrice > product.price ? `<div class="original-price">$${product.originalPrice.toFixed(2)}</div>` : ''}
                    <div class="current-price">$${product.price.toFixed(2)}</div>
                </div>
                <div class="actions">
                    <div class="quantity-selector">
                        <button class="qty-btn" onclick="updateQuantity(${product.id}, -1)">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" class="qty-input" id="qty-${product.id}" value="1" min="1" max="99">
                        <button class="qty-btn" onclick="updateQuantity(${product.id}, 1)">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <button class="add-to-quote-btn" onclick="addToQuote(${product.id})" ${!product.inStock ? 'disabled' : ''}>
                        <i class="fas fa-plus"></i> Add
                    </button>
                </div>
            </div>
        `).join('')}
    `;
}

function renderGridView(container) {
    container.innerHTML = filteredProducts.map(product => `
        <div class="product-grid-item ${!product.inStock ? 'out-of-stock' : ''}">
            <div class="grid-image">
                <img src="${product.image}" alt="${product.name}">
                ${!product.inStock ? '<div class="out-of-stock-overlay">Out of Stock</div>' : ''}
                ${product.originalPrice > product.price ? `<div class="discount-badge">${Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF</div>` : ''}
            </div>
            <div class="grid-content">
                <h4>${product.name}</h4>
                <div class="brand">${product.brand}</div>
                <div class="rating-section">
                    <div class="stars">${generateStars(product.rating)}</div>
                    <span class="rating-text">${product.rating}</span>
                </div>
                <div class="grid-price">$${product.price.toFixed(2)}</div>
                <div class="compatibility">
                    ${product.compatibility.slice(0, 3).map(type => `<span class="vehicle-type">${type.charAt(0).toUpperCase() + type.slice(1)}</span>`).join('')}
                </div>
                <div class="grid-actions">
                    <div class="grid-qty-controls">
                        <button class="grid-qty-btn" onclick="updateQuantity(${product.id}, -1)">
                            <i class="fas fa-minus"></i>
                        </button>
                        <span class="grid-qty-display" id="grid-qty-${product.id}">1</span>
                        <button class="grid-qty-btn" onclick="updateQuantity(${product.id}, 1)">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <button class="grid-add-btn" onclick="addToQuote(${product.id})" ${!product.inStock ? 'disabled' : ''}>
                        <i class="fas fa-cart-plus"></i>
                    </button>
                </div>
                <div class="stock-info ${product.stockQuantity < 10 ? 'low-stock' : ''}">
                    <i class="fas ${product.inStock ? 'fa-check' : 'fa-times'}"></i>
                    ${product.inStock ? `${product.stockQuantity} available` : 'Out of stock'}
                </div>
            </div>
        </div>
    `).join('');
}

// Utility Functions
function generateStars(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        stars += `<i class="fas fa-star ${i <= Math.floor(rating) ? 'filled' : ''}"></i>`;
    }
    return stars;
}

function getCategoryName(category) {
    const categoryMap = {
        'performance': 'Performance Parts',
        'exterior': 'Exterior Accessories',
        'interior': 'Interior Accessories',
        'lighting': 'Lighting',
        'audio': 'Audio & Electronics',
        'wheels': 'Wheels & Tires',
        'suspension': 'Suspension',
        'exhaust': 'Exhaust Systems'
    };
    return categoryMap[category] || category;
}

function generateQuoteNumber() {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substring(2, 5).toUpperCase();
    const quoteNumber = `Q${timestamp}${random}`;
    document.getElementById('quote-number').textContent = quoteNumber;
    return quoteNumber;
}

function setQuoteDate() {
    const today = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    document.getElementById('quote-date').textContent = today;
}

function populateYearDropdown() {
    const yearSelect = document.querySelector('select[name="year"]');
    const currentYear = new Date().getFullYear();
    
    for (let year = currentYear; year >= 1990; year--) {
        const option = document.createElement('option');
        option.value = year;
        option.textContent = year;
        yearSelect.appendChild(option);
    }
}

// Quantity Management
function updateQuantity(productId, change) {
    const qtyInput = document.getElementById(`qty-${productId}`);
    const gridQtyDisplay = document.getElementById(`grid-qty-${productId}`);
    
    if (qtyInput) {
        let currentQty = parseInt(qtyInput.value) || 1;
        let newQty = currentQty + change;
        newQty = Math.max(1, Math.min(99, newQty));
        qtyInput.value = newQty;
    }
    
    if (gridQtyDisplay) {
        let currentQty = parseInt(gridQtyDisplay.textContent) || 1;
        let newQty = currentQty + change;
        newQty = Math.max(1, Math.min(99, newQty));
        gridQtyDisplay.textContent = newQty;
    }
}

// Quote Management
function addToQuote(productId) {
    const product = products.find(p => p.id === productId);
    if (!product || !product.inStock) return;
    
    const qtyInput = document.getElementById(`qty-${productId}`);
    const gridQtyDisplay = document.getElementById(`grid-qty-${productId}`);
    const quantity = qtyInput ? parseInt(qtyInput.value) : parseInt(gridQtyDisplay?.textContent) || 1;
    
    const existingItem = quoteItems.find(item => item.id === productId);
    
    if (existingItem) {
        existingItem.quantity += quantity;
    } else {
        quoteItems.push({
            ...product,
            quantity: quantity,
            addedAt: new Date().toISOString()
        });
    }
    
    // Reset quantity inputs
    if (qtyInput) qtyInput.value = 1;
    if (gridQtyDisplay) gridQtyDisplay.textContent = 1;
    
    updateQuoteDisplay();
    showSuccessMessage(`Added ${quantity}x ${product.name} to quote`);
}

function updateQuoteDisplay() {
    const count = quoteItems.reduce((sum, item) => sum + item.quantity, 0);
    const total = calculateTotal();
    
    document.getElementById('quote-count').textContent = count;
    document.getElementById('quote-total').textContent = total.toFixed(2);
}

function calculateTotal() {
    const subtotal = quoteItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const tax = subtotal * 0.08;
    return subtotal + tax;
}

function renderQuoteItems() {
    const container = document.getElementById('quote-items');
    const totalsContainer = document.getElementById('quote-totals');
    
    if (quoteItems.length === 0) {
        container.innerHTML = `
            <div class="empty-quote">
                <i class="fas fa-shopping-cart"></i>
                <h4>No items in quote</h4>
                <p>Add some products to get started</p>
                <button class="btn btn-primary" onclick="showProducts()">
                    <i class="fas fa-plus"></i> Browse Products
                </button>
            </div>
        `;
        totalsContainer.style.display = 'none';
        return;
    }
    
    container.innerHTML = `
        <div class="items-header">
            <div>Product</div>
            <div>Unit Price</div>
            <div>Quantity</div>
            <div>Total</div>
            <div>Actions</div>
        </div>
        ${quoteItems.map(item => `
            <div class="quote-item">
                <div class="item-info">
                    <img src="${item.image}" alt="${item.name}" class="item-image">
                    <div class="item-details">
                        <h4>${item.name}</h4>
                        <p>${item.brand}</p>
                        <small>Part #: ${item.partNumber}</small>
                    </div>
                </div>
                <div class="unit-price">$${item.price.toFixed(2)}</div>
                <div class="quantity-controls">
                    <button class="qty-btn" onclick="updateQuoteQuantity(${item.id}, -1)">
                        <i class="fas fa-minus"></i>
                    </button>
                    <input type="number" value="${item.quantity}" min="1" max="99" onchange="setQuoteQuantity(${item.id}, this.value)">
                    <button class="qty-btn" onclick="updateQuoteQuantity(${item.id}, 1)">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <div class="item-total">$${(item.price * item.quantity).toFixed(2)}</div>
                <div class="item-actions">
                    <button class="remove-btn" onclick="removeFromQuote(${item.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('')}
    `;
    
    updateQuoteTotals();
    totalsContainer.style.display = 'flex';
}

function updateQuoteQuantity(itemId, change) {
    const item = quoteItems.find(item => item.id === itemId);
    if (item) {
        item.quantity = Math.max(1, Math.min(99, item.quantity + change));
        renderQuoteItems();
        updateQuoteDisplay();
    }
}

function setQuoteQuantity(itemId, quantity) {
    const item = quoteItems.find(item => item.id === itemId);
    if (item) {
        item.quantity = Math.max(1, Math.min(99, parseInt(quantity) || 1));
        renderQuoteItems();
        updateQuoteDisplay();
    }
}

function removeFromQuote(itemId) {
    quoteItems = quoteItems.filter(item => item.id !== itemId);
    renderQuoteItems();
    updateQuoteDisplay();
}

function clearQuote() {
    if (confirm('Are you sure you want to clear the entire quote?')) {
        quoteItems = [];
        renderQuoteItems();
        updateQuoteDisplay();
        showSuccessMessage('Quote cleared successfully');
    }
}

function updateQuoteTotals() {
    const subtotal = quoteItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const tax = subtotal * 0.08;
    const total = subtotal + tax;
    
    document.getElementById('subtotal').textContent = `$${subtotal.toFixed(2)}`;
    document.getElementById('tax').textContent = `$${tax.toFixed(2)}`;
    document.getElementById('total').textContent = `$${total.toFixed(2)}`;
}

// Customer Management
function showCustomerForm() {
    document.getElementById('customer-modal').classList.add('active');
}

function closeCustomerForm() {
    document.getElementById('customer-modal').classList.remove('active');
}

function saveCustomerInfo(event) {
    event.preventDefault();
    const formData = new FormData(event.target);
    
    customerInfo = {
        name: formData.get('name'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        year: formData.get('year'),
        make: formData.get('make'),
        model: formData.get('model')
    };
    
    displayCustomerInfo();
    closeCustomerForm();
    showSuccessMessage('Customer information saved successfully');
}

function displayCustomerInfo() {
    const container = document.getElementById('customer-info');
    const detailsContainer = document.querySelector('.customer-details');
    
    if (customerInfo.name || customerInfo.email) {
        detailsContainer.innerHTML = `
            <div class="customer-detail">
                <span class="label">Name:</span>
                <span>${customerInfo.name || 'Not provided'}</span>
            </div>
            <div class="customer-detail">
                <span class="label">Email:</span>
                <span>${customerInfo.email || 'Not provided'}</span>
            </div>
            <div class="customer-detail">
                <span class="label">Phone:</span>
                <span>${customerInfo.phone || 'Not provided'}</span>
            </div>
            <div class="customer-detail">
                <span class="label">Vehicle:</span>
                <span>${[customerInfo.year, customerInfo.make, customerInfo.model].filter(Boolean).join(' ') || 'Not specified'}</span>
            </div>
        `;
        container.style.display = 'block';
    }
}

// Export Functions
function exportPDF() {
    showSuccessMessage('PDF export feature coming soon!');
}

function printQuote() {
    window.print();
}

function sendQuote() {
    if (quoteItems.length === 0) {
        alert('Please add items to the quote before sending.');
        return;
    }
    showSuccessMessage('Quote sent successfully!');
}

// Utility Functions
function showSuccessMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'success-message';
    messageDiv.innerHTML = `<i class="fas fa-check"></i> ${message}`;
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

// Quick View Functions
function showQuickView(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;
    
    const content = document.getElementById('quick-view-content');
    content.innerHTML = `
        <div class="quick-view-body">
            <img src="${product.image}" alt="${product.name}" class="quick-view-image">
            <div class="quick-view-info">
                <h3>${product.name}</h3>
                <p class="brand">${product.brand}</p>
                <p class="description">${product.description}</p>
                <div class="features">
                    <h4>Features:</h4>
                    <ul>
                        ${product.features.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                </div>
                <div class="warranty">Warranty: ${product.warranty}</div>
                <div class="part-number">Part Number: ${product.partNumber}</div>
            </div>
        </div>
    `;
    
    document.getElementById('quick-view-modal').classList.add('active');
}

function closeQuickView() {
    document.getElementById('quick-view-modal').classList.remove('active');
}
