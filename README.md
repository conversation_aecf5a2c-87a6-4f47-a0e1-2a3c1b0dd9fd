# Vehicle Aftermarket Quotation System

A modern, responsive web application for creating quotations for vehicle aftermarket products. Built with Vue.js 3, featuring multiple view options (Card, List, Grid) and comprehensive quotation management.

## Features

### 🚗 Product Catalog
- **Multiple View Options**: Switch between Card, List, and Grid views
- **Advanced Filtering**: Search by name, brand, category, and vehicle compatibility
- **Product Details**: Comprehensive product information including images, specifications, and compatibility
- **Real-time Stock Status**: Live inventory tracking with low-stock warnings

### 📋 Quotation Management
- **Dynamic Quote Building**: Add products with quantity selection
- **Customer Information**: Capture and store customer and vehicle details
- **Real-time Calculations**: Automatic subtotal, tax, and total calculations
- **Quote Summary**: Detailed breakdown of all selected items

### 🎨 User Experience
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Modern UI**: Clean, professional interface with smooth animations
- **Intuitive Navigation**: Easy switching between product browsing and quote management
- **Quick Actions**: Fast product addition and quantity management

## Technology Stack

- **Frontend**: Vue.js 3 with Composition API
- **State Management**: Pinia
- **Routing**: Vue Router 4
- **Styling**: SCSS with responsive design
- **Build Tool**: Vite
- **Icons**: Font Awesome 6

## Project Structure

```
src/
├── components/           # Reusable Vue components
│   ├── ProductCard.vue   # Card view component
│   ├── ProductList.vue   # List view component
│   ├── ProductGrid.vue   # Grid view component
│   └── CustomerForm.vue  # Customer information form
├── views/               # Page components
│   ├── ProductCatalog.vue # Main product browsing page
│   └── QuoteSummary.vue   # Quote management page
├── stores/              # Pinia stores
│   └── quotation.js     # Quote state management
├── data/                # Mock data and models
│   └── products.js      # Product data and categories
├── styles/              # Global styles
│   └── main.scss        # Main stylesheet
├── router/              # Vue Router configuration
│   └── index.js         # Route definitions
└── main.js              # Application entry point
```

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd QUOTATION
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:3000`

### Build for Production

```bash
npm run build
```

### Preview Production Build

```bash
npm run preview
```

## Usage Guide

### Browsing Products

1. **View Selection**: Use the view toggle buttons to switch between:
   - **Card View**: Detailed product cards with images and full information
   - **List View**: Compact tabular format for quick comparison
   - **Grid View**: Optimized grid layout for efficient browsing

2. **Filtering**: Use the search bar and dropdown filters to find specific products:
   - Search by product name, brand, or description
   - Filter by product category
   - Filter by vehicle compatibility

3. **Adding to Quote**: 
   - Select desired quantity using the quantity controls
   - Click "Add to Quote" button
   - View running total in the header

### Managing Quotes

1. **Quote Summary**: Click "View Quote" to see all selected items
2. **Customer Information**: Add customer and vehicle details
3. **Quantity Management**: Adjust quantities or remove items
4. **Export Options**: Generate PDF or print quotes (coming soon)

## Product Categories

- Performance Parts
- Exterior Accessories  
- Interior Accessories
- Lighting
- Audio & Electronics
- Wheels & Tires
- Suspension
- Exhaust Systems

## Vehicle Compatibility

The system supports products for various vehicle types:
- Sedan
- SUV
- Truck
- Coupe
- Hatchback
- Convertible

## Responsive Design

The application is fully responsive and optimized for:
- **Desktop**: Full-featured experience with all view options
- **Tablet**: Adapted layouts with touch-friendly controls
- **Mobile**: Streamlined interface with collapsible sections

## Future Enhancements

- [ ] PDF export functionality
- [ ] Email quote sending
- [ ] User authentication
- [ ] Order processing
- [ ] Inventory management
- [ ] Multi-language support
- [ ] Advanced reporting

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please open an issue in the repository or contact the development team.

---

Built with ❤️ for the automotive aftermarket industry
