# 🚗 AutoParts Pro - Vehicle Aftermarket Quotation System

A modern, responsive web application for creating professional quotations for vehicle aftermarket products. Features three distinct view modes and comprehensive quotation management.

## ✨ Features

### 🎯 Multiple View Options
- **Card View**: Detailed product cards with rich information and imagery
- **List View**: Compact tabular format for quick product comparison
- **Grid View**: Optimized grid layout for efficient product browsing

### 🔍 Advanced Product Management
- **Smart Search**: Search by product name, brand, description, or category
- **Category Filtering**: Filter by Performance Parts, Lighting, Exterior/Interior Accessories, etc.
- **Vehicle Compatibility**: Filter products by vehicle type (Sedan, SUV, Truck, etc.)
- **Real-time Stock Status**: Live inventory tracking with low-stock warnings
- **Product Details**: Comprehensive specs, features, ratings, and compatibility info

### 📋 Professional Quotation System
- **Dynamic Quote Building**: Add products with flexible quantity selection
- **Customer Management**: Capture customer contact and vehicle information
- **Real-time Calculations**: Automatic subtotal, tax (8%), and total calculations
- **Quote Summary**: Detailed breakdown with item management capabilities
- **Export Options**: PDF export and print functionality (coming soon)

### 🎨 Modern User Experience
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Smooth Animations**: Professional transitions and hover effects
- **Intuitive Interface**: Clean, modern design with excellent usability
- **Success Notifications**: Real-time feedback for user actions

## 🚀 Quick Start

### Option 1: Simple File Opening
1. **Download all files** to your computer
2. **Open `main.html`** in any modern web browser
3. **Start browsing** and creating quotations immediately!

### Option 2: Local Server (Recommended)
1. **Install a simple HTTP server**:
   ```bash
   npm install -g http-server
   ```

2. **Navigate to project directory** and start server:
   ```bash
   cd QUOTATION
   http-server -p 3000
   ```

3. **Open browser** and go to `http://localhost:3000/main.html`

## 📱 How to Use

### Product Browsing
1. **Switch Views**: Use the Card/List/Grid toggle buttons in the top-right
2. **Search Products**: Use the search bar to find specific items
3. **Apply Filters**: Select category and vehicle type filters
4. **Add to Quote**: Select quantity and click "Add to Quote"

### Quote Management
1. **View Quote**: Click the "View Quote" button to see selected items
2. **Customer Info**: Add customer and vehicle details
3. **Manage Items**: Adjust quantities or remove items from quote
4. **Export**: Generate PDF or print the quotation

## 🛠 Technical Details

### Built With
- **HTML5**: Semantic markup with accessibility features
- **CSS3**: Modern styling with Flexbox/Grid and animations
- **Vanilla JavaScript**: No frameworks - pure, fast JavaScript
- **Font Awesome**: Professional icons
- **Google Fonts**: Poppins font family for modern typography

### Browser Support
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 📊 Sample Products Included

The system comes with 8 sample aftermarket products:

1. **Cold Air Intake System** (K&N) - $299.99
2. **LED Headlight Kit** (Philips) - $189.99
3. **Carbon Fiber Spoiler** (Seibon) - $459.99
4. **Racing Seat Covers** (Sparco) - $129.99
5. **Performance Exhaust System** (Borla) - $899.99
6. **Lowering Springs** (Eibach) - $249.99
7. **Premium Floor Mats** (WeatherTech) - $179.99
8. **Alloy Wheel Set** (BBS) - $1,299.99

## 🎨 View Demonstrations

### Card View
Perfect for detailed product exploration with:
- Large product images
- Complete feature lists
- Compatibility information
- Customer ratings and reviews
- Pricing with discount badges

### List View
Ideal for quick comparison with:
- Compact tabular layout
- Essential product information
- Quick quantity selection
- Efficient browsing experience

### Grid View
Optimized for fast browsing with:
- Clean, minimal design
- Quick add-to-cart functionality
- Essential product details
- Responsive grid layout

## 🔧 Customization

### Adding New Products
Edit the `products` array in `script.js`:

```javascript
{
    id: 9,
    name: 'Your Product Name',
    category: 'performance', // performance, exterior, interior, lighting, audio, wheels, suspension, exhaust
    brand: 'Brand Name',
    price: 199.99,
    originalPrice: 249.99, // Optional for discounts
    description: 'Product description',
    image: 'https://your-image-url.com/image.jpg',
    compatibility: ['sedan', 'suv'], // sedan, suv, truck, coupe, hatchback
    inStock: true,
    stockQuantity: 20,
    rating: 4.5,
    reviews: 75,
    features: ['Feature 1', 'Feature 2', 'Feature 3'],
    warranty: '2 years',
    partNumber: 'PART-123'
}
```

### Styling Customization
- **Colors**: Modify CSS custom properties in `styles.css`
- **Layout**: Adjust grid templates and spacing
- **Typography**: Change font families and sizes
- **Animations**: Customize transition timings and effects

## 📈 Future Enhancements

- [ ] PDF generation with jsPDF
- [ ] Email integration for quote sending
- [ ] User authentication and saved quotes
- [ ] Advanced product filtering (price range, ratings)
- [ ] Product comparison feature
- [ ] Inventory management system
- [ ] Multi-language support
- [ ] Advanced reporting and analytics

## 🤝 Contributing

Feel free to fork this project and submit pull requests for improvements!

## 📄 License

This project is open source and available under the MIT License.

---

**Ready to start creating professional vehicle aftermarket quotations!** 🚀
